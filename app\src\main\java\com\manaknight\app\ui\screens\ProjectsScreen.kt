package com.manaknight.app.ui.screens

import Manaknight.R
import android.app.Dialog
import android.util.Log
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.*
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.*
import com.manaknight.app.model.remote.ProjectResponseModel
import com.manaknight.app.model.remote.list
import com.manaknight.app.model.remote.profitPro.MaterialItem
import com.manaknight.app.network.Resource
import com.manaknight.app.network.Status
import com.manaknight.app.viewmodels.BaasViewModel
import com.manaknight.app.ui.utils.isTabletLayout
import com.manaknight.app.ui.components.ResponsiveSheetContainer
import com.manaknight.app.ui.components.CustomCheckbox
import kotlinx.coroutines.launch
import java.time.LocalDate
import java.time.format.DateTimeFormatter

// Projects Screen Composable
@Composable
fun ProjectsScreen(userId: Int, viewModel: BaasViewModel, onProjectClick: (Int) -> Unit, dialog: Dialog, onNavigateToLineItems: (Int, String) -> Unit, onNavigateToViewEstimate: (Int) -> Unit, onNavigateToCreateEstimate: () -> Unit, initialSelectedStatus: String = "All") {
    val userId = remember { userId }
    LaunchedEffect(userId) {
        viewModel.setUserId(userId)
    }

    val projectsResource by viewModel.projectsResource.observeAsState()
    var showStatusFilter by remember { mutableStateOf(false) }
    var selectedStatuses by remember { mutableStateOf(setOf(initialSelectedStatus)) }
    var showMonthFilter by remember { mutableStateOf(false) }
    var selectedMonth by remember { mutableStateOf("All") } // Default to "All"
    Surface(color = Color.White) {
        Column(modifier = Modifier.padding(horizontal = 16.dp, vertical = 16.dp).background(Color.White)) {
            // Header - always full width
            Header(onNavigateToCreateEstimate)

            Spacer(modifier = Modifier.height(8.dp))

            val isTablet = isTabletLayout()
            if (isTablet) {
                Box(
                    modifier = Modifier.fillMaxWidth(),
                    contentAlignment = Alignment.TopCenter
                ) {
                Box(modifier = Modifier.widthIn(max = 500.dp)) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                StatusFilterDropdown(selectedStatuses) {
                    showStatusFilter = true
                }
                MonthFilterDropdown(selectedMonth) {
                    showMonthFilter = true
                }
            }
            }}}
            else{
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    StatusFilterDropdown(selectedStatuses) {
                        showStatusFilter = true
                    }
                    MonthFilterDropdown(selectedMonth) {
                        showMonthFilter = true
                    }
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            // Project list content - constrained width on tablet
            ProjectListContent(
                projectsResource,
                selectedStatuses,
                onProjectClick,
                dialog,
                selectedMonth,
                onNavigateToLineItems,
                onNavigateToViewEstimate
            )
        }
    }
    if (showStatusFilter) {
        MultiSelectStatusFilterSheet(
            selectedStatuses = selectedStatuses,
            onDismiss = { showStatusFilter = false },
            onStatusesChanged = { newStatuses ->
                selectedStatuses = newStatuses
            }
        )
    }
    if (showMonthFilter) {
        MonthFilterSheet(
            onDismiss = { showMonthFilter = false },
            onMonthSelected = { month ->
                selectedMonth = month
                showMonthFilter = false
            }
        )
    }
}


// Header Composable
@Composable
fun Header(onNavigateToCreateEstimate: () -> Unit) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(text = "Projects", fontSize = 20.sp, fontWeight = FontWeight.Bold )
        Button(
            onClick = {onNavigateToCreateEstimate()},
            shape = RoundedCornerShape(4.dp),
            colors = ButtonDefaults.buttonColors(containerColor = colorResource(R.color.white)),
            border = BorderStroke(1.dp, colorResource(R.color.profit_blue)),
            contentPadding = PaddingValues(horizontal = 8.dp, vertical = 2.dp), // Tight padding
            modifier = Modifier
                .padding(2.dp)
                .height(28.dp)
                .wrapContentWidth() // Shrinks button to fit content
                .defaultMinSize(minWidth = 0.dp) // Prevents enforcing min width
        ) {
            Text(
                text = "New",
                fontSize = 16.sp,
                color = colorResource(R.color.profit_blue)
            )
            Spacer(modifier = Modifier.width(4.dp)) // Space between text and icon
            Icon(
                painter = painterResource(id = R.drawable.ic_add),
                contentDescription = "Add Project",
                tint = colorResource(R.color.profit_blue),
                modifier = Modifier.size(16.dp)
            )
        }

    }
}

// Filters Composable
@Composable
fun Filters() {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        DropdownFilter("Status: All")
        DropdownFilter("This month")
    }
}

// Dropdown Filter Button Composable
@Composable
fun DropdownFilter(label: String) {
    Button(
        onClick = { /* Handle dropdown */ },
        colors = ButtonDefaults.buttonColors(colorResource(R.color.white)),
        shape = RoundedCornerShape(8.dp)
    ) {
        Text(text = label, color = Color.Black)
        Icon(
            painter = painterResource(id = R.drawable.ic_dropdown), // Replace with your actual icon
            tint = colorResource(R.color.text_sub),
            contentDescription = "Filter Project",
            modifier = Modifier.size(20.dp)
        )

    }
}

// Status Filter Dropdown Composable
@Composable
fun StatusFilterDropdown(selectedStatuses: Set<String>, onClick: () -> Unit) {
    val displayText = when {
        selectedStatuses.isEmpty() -> "Status: None"
        selectedStatuses.size == 1 -> "Status: ${selectedStatuses.first()}"
        selectedStatuses.contains("All") -> "Status: All"
        else -> "Status: ${selectedStatuses.size} selected"
    }

    Button(
        onClick = onClick,
        shape = RoundedCornerShape(4.dp),
        colors = ButtonDefaults.buttonColors(containerColor = colorResource(R.color.white)),
        border = BorderStroke(1.dp, colorResource(R.color.stroke_soft)),
        contentPadding = PaddingValues(horizontal = 8.dp, vertical = 2.dp), // Tight padding
        modifier = Modifier
            .padding(2.dp)
            .height(28.dp)
            .wrapContentWidth() // Shrinks button to fit content
            .defaultMinSize(minWidth = 0.dp) // Prevents enforcing min width
    ) {
        Text(
            text = displayText,
            fontSize = 16.sp,
            color = colorResource(R.color.text_sub)
        )
        Spacer(modifier = Modifier.width(4.dp)) // Space between text and icon
        Icon(
            painter = painterResource(id = R.drawable.ic_dropdown), // Replace with your actual icon
            tint = colorResource(R.color.text_sub),
            contentDescription = "Filter Status",
            modifier = Modifier.size(20.dp)
        )
    }
}

// Month Filter Section
@Composable
fun MonthFilterSection(
    selectedMonth: String,
    onShowMonthFilter: () -> Unit,
    onMonthSelected: (String) -> Unit
) {
    MonthFilterDropdown(selectedMonth, onShowMonthFilter)
    if (remember { mutableStateOf(false) }.value) { // Placeholder for triggering the sheet
        MonthFilterSheet(
            onDismiss = { /* Handle dismiss */ },
            onMonthSelected = onMonthSelected
        )
    }
}

// Month Filter Dropdown Composable
@Composable
fun MonthFilterDropdown(selectedMonth: String, onClick: () -> Unit) {
    Button(
        onClick = onClick,
        shape = RoundedCornerShape(4.dp),
        colors = ButtonDefaults.buttonColors(containerColor = colorResource(R.color.white)),
        border = BorderStroke(1.dp, colorResource(R.color.stroke_soft)),
        contentPadding = PaddingValues(horizontal = 8.dp, vertical = 2.dp), // Tight padding
        modifier = Modifier
            .padding(2.dp)
            .height(28.dp)
            .wrapContentWidth() // Shrinks button to fit content
            .defaultMinSize(minWidth = 0.dp) // Prevents enforcing min width
    ) {
        Text(
            text =  selectedMonth,
            fontSize = 16.sp,
            color = colorResource(R.color.text_sub)
        )
        Spacer(modifier = Modifier.width(4.dp)) // Space between text and icon
        Icon(
            painter = painterResource(id = R.drawable.ic_dropdown), // Replace with your actual icon
            tint = colorResource(R.color.text_sub),
            contentDescription = "Filter Month",
            modifier = Modifier.size(20.dp)
        )
    }



}

// Month Filter Sheet Composable (Separate Implementation)
@Composable
fun MonthFilterSheet(onDismiss: () -> Unit, onMonthSelected: (String) -> Unit) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black.copy(alpha = 0.5f))
            .clickable { onDismiss() },
        contentAlignment = Alignment.BottomCenter
    ) {
        Surface(
            modifier = Modifier
                .fillMaxWidth()
                .heightIn(min = 200.dp, max = 400.dp),
            color = colorResource(R.color.white),
            shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
        ) {
            Column(modifier = Modifier.padding(16.dp)) {
//                Text("Select Month", fontWeight = FontWeight.Bold, fontSize = 18.sp)
                Spacer(Modifier.height(8.dp))
                MonthOption("All", onMonthSelected)
                MonthOption("This month", onMonthSelected)
                MonthOption("Last month", onMonthSelected)
                MonthOption("January", onMonthSelected)
                MonthOption("February", onMonthSelected)
                MonthOption("March", onMonthSelected)
                MonthOption("April", onMonthSelected)
                MonthOption("May", onMonthSelected)
                MonthOption("June", onMonthSelected)
                MonthOption("July", onMonthSelected)
                MonthOption("August", onMonthSelected)
                MonthOption("September", onMonthSelected)
                MonthOption("October", onMonthSelected)
                MonthOption("November", onMonthSelected)
                MonthOption("December", onMonthSelected)
            }
        }
    }
}

// Monthly Filter Sheet Composable (Custom Sheet Implementation)
@Composable
fun MonthlyFilterSheet(onDismiss: () -> Unit, onStatusSelected: (String) -> Unit) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black.copy(alpha = 0.5f)) // Optional: Add a scrim
            .clickable { onDismiss() }, // Close on scrim click
        contentAlignment = Alignment.BottomCenter
    ) {
        Surface(
            modifier = Modifier
                .fillMaxWidth()
                .height(300.dp), // Adjust height as needed

            color = MaterialTheme.colorScheme.surface,
            shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
        ) {
            Column(modifier = Modifier.padding(16.dp)) {
                Text("Select Status", fontWeight = FontWeight.Bold, fontSize = 18.sp)
                Spacer(Modifier.height(8.dp))
                StatusOption("All", onStatusSelected)
                StatusOption("Draft", onStatusSelected)
                StatusOption("Outstanding", onStatusSelected)
                StatusOption("Active", onStatusSelected)
                StatusOption("Completed", onStatusSelected)
            }
        }
    }
}
// Multi-Select Status Filter Sheet Composable
@Composable
fun MultiSelectStatusFilterSheet(
    selectedStatuses: Set<String>,
    onDismiss: () -> Unit,
    onStatusesChanged: (Set<String>) -> Unit
) {
    val statusOptions = listOf("All", "Draft", "Outstanding", "Active", "Completed")

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black.copy(alpha = 0.5f))
            .clickable { onDismiss() },
        contentAlignment = Alignment.BottomCenter
    ) {
        Surface(
            modifier = Modifier
                .fillMaxWidth()
                .height(400.dp),
            color = colorResource(R.color.white),
            shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
        ) {
            Column(modifier = Modifier.padding(16.dp)) {
                Text(
                    text = "Select Status",
                    fontWeight = FontWeight.Bold,
                    fontSize = 18.sp,
                    color = colorResource(R.color.profit_black),
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                statusOptions.forEach { status ->
                    MultiSelectStatusOption(
                        status = status,
                        isSelected = selectedStatuses.contains(status),
                        onSelectionChanged = { isSelected ->
                            val newStatuses = if (status == "All") {
                                if (isSelected) setOf("All") else emptySet()
                            } else {
                                val updatedStatuses = selectedStatuses.toMutableSet()
                                updatedStatuses.remove("All") // Remove "All" when selecting specific statuses
                                if (isSelected) {
                                    updatedStatuses.add(status)
                                } else {
                                    updatedStatuses.remove(status)
                                }
                                updatedStatuses
                            }
                            onStatusesChanged(newStatuses)
                        }
                    )
                }
            }
        }
    }
}

// Multi-Select Status Option Composable with Custom Checkbox
@Composable
fun MultiSelectStatusOption(
    status: String,
    isSelected: Boolean,
    onSelectionChanged: (Boolean) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onSelectionChanged(!isSelected) }
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        CustomCheckbox(
            checked = isSelected,
            onCheckedChange = { onSelectionChanged(it) }
        )
        Text(
            text = status,
            modifier = Modifier.padding(start = 12.dp),
            color = colorResource(R.color.profit_black),
            fontSize = 16.sp
        )
    }
}

// Status Option Composable (for backward compatibility with other screens)
@Composable
fun StatusOption(status: String, onStatusSelected: (String) -> Unit) {
    Text(
        text = status,
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onStatusSelected(status) }
            .padding(vertical = 8.dp),
         color = colorResource(R.color.profit_black),
        fontSize = 16.sp
    )
}

// Month Option Composable
@Composable
fun MonthOption(month: String, onMonthSelected: (String) -> Unit) {
    Text(
        text = month,
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onMonthSelected(month) }
            .padding(vertical = 8.dp),
        color = colorResource(R.color.profit_black),
        fontSize = 16.sp
    )
}
// Project List Content Composable
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProjectListContent(projectsResource: Resource<ProjectResponseModel>?, selectedStatuses: Set<String>, onProjectClick: (Int) -> Unit, dialog:Dialog, selectedMonth: String,onNavigateToLineItems: (Int, String) -> Unit,
                       onNavigateToViewEstimate: (Int) -> Unit) {

    var showBottomSheet by remember { mutableStateOf(false) }
    var selectedProjectId by remember { mutableStateOf<Int?>(null) }
    var selectedProjectName by remember { mutableStateOf<String?>(null) }
    val sheetState = rememberModalBottomSheetState()
    val scope = rememberCoroutineScope()

    when (projectsResource?.status) {
        Status.LOADING -> dialog.show()
        Status.ERROR -> {
            Text(text = "Error: ${projectsResource.message}", color = Color.Red)
            dialog.hide()

        }
        Status.SUCCESS -> {
            dialog.hide()
            projectsResource.data?.list?.let { projectList ->
                val filteredList = projectList.filter { project ->
                    val statusFilterPassed = if (selectedStatuses.contains("All")) {
                        true
                    } else {
                        val statusText = when (project.status) {
                            1 -> "Active"
                            2 -> "Outstanding"
                            3 -> "Draft"
                            4 -> "Completed"
                            else -> "Unknown"
                        }
                        selectedStatuses.contains(statusText)
                    }

                    val monthFilterPassed = if (selectedMonth == "All") {
                        true
                    } else {
                        val dateFormatter = DateTimeFormatter.ISO_DATE
                        val projectDate = try {
                            LocalDate.parse(project.create_at, dateFormatter)
                        } catch (e: Exception) {
                            null
                        }

                        when (selectedMonth) {
                            "This month" -> projectDate?.year == LocalDate.now().year && projectDate?.month == LocalDate.now().month
                            "Last month" -> projectDate?.year == LocalDate.now().year && projectDate?.month == LocalDate.now().minusMonths(1).month
                            else -> projectDate?.month.toString().equals(selectedMonth, ignoreCase = true)
                        } ?: false
                    }
                    statusFilterPassed && monthFilterPassed
                }

                if (showBottomSheet && selectedProjectId != null) {
                    ResponsiveSheetContainer(
                        showSheet = showBottomSheet,
                        onDismiss = {
                            showBottomSheet = false
                            selectedProjectId = null
                            selectedProjectName = null
                        },
                        sheetState = sheetState,
                        headerContent = {
                            ProjectBottomSheetHeader(
                                onDismiss = {
                                    showBottomSheet = false
                                    selectedProjectId = null
                                    selectedProjectName = null
                                }
                            )
                        },
                        content = {
                            ProjectBottomSheetContentBody(
                                onOptionClick = { option ->
                                    // Capture values before nulling them
                                    val projectId = selectedProjectId
                                    val projectName = selectedProjectName

                                    showBottomSheet = false
                                    selectedProjectId = null
                                    selectedProjectName = null

                                    Log.d("ProjectsScreen", "Project ID: ${projectId}, Name: ${projectName}")

                                    // Use the captured values
                                    when (option) {
                                        "Change Order" -> projectId?.let { id ->
                                            projectName?.let { name ->
                                                onNavigateToLineItems(id, name)
                                            }
                                        }
                                        "View Estimate" -> projectId?.let { id ->
//                                            onNavigateToViewEstimate(id)
                                            onProjectClick(id)

                                        }
                                    }
                                }
                            )
                        },
                        fraction = 0.3f
                    )
                }

                if (filteredList.isEmpty()) {
                    Text("No projects available with the selected criteria.")
                } else {
                    // Apply width constraint only to the project list on tablets
                    val isTablet = isTabletLayout()
                    if (isTablet) {
                        Box(
                            modifier = Modifier.fillMaxWidth(),
                            contentAlignment = Alignment.TopCenter
                        ) {
                            Box(modifier = Modifier.widthIn(max = 500.dp)) {
                                ProjectList(
                                    projects = filteredList,
                                    onProjectClick = onProjectClick,
                                    onMoreClick = { project ->
                                        Log.d("ProjectsScreen", "Nam: ${project}")
                                        selectedProjectId = project.id
                                        selectedProjectName = project.name ?: "Default Project Name"
                                        showBottomSheet = true
                                    }
                                )
                            }
                        }
                    } else {
                        ProjectList(
                            projects = filteredList,
                            onProjectClick = onProjectClick,
                            onMoreClick = { project ->
                                Log.d("ProjectsScreen", "Nam: ${project}")
                                selectedProjectId = project.id
                                selectedProjectName = project.name ?: "Default Project Name"
                                showBottomSheet = true
                            }
                        )
                    }
                }
            } ?: Text("No projects available")
        }
        else -> Text(text = "No Data Available")
    }
}

// Project List Composable - Same layout for mobile and tablet
@Composable
fun ProjectList(
    projects: List<list>,
    onProjectClick: (Int) -> Unit,
    onMoreClick: (list) -> Unit
) {
    // Same spacing for both mobile and tablet
    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(8.dp),
        contentPadding = PaddingValues(vertical = 4.dp)
    ) {
        items(projects) { project ->
            ProjectCard(
                project = project,
                onClick = { onProjectClick(project.id) },
                onMoreClick = { onMoreClick(project) }
            )
        }
    }
}

// Project Card Composable
@Composable
fun ProjectCard(project: list, onClick: () -> Unit,  onMoreClick:() -> Unit) {
    val statusText = when (project.status) {
        0 -> "Inactive"
        1 -> "Active"
        2 -> "Outstanding"
        3 -> "Draft"
        4 -> "Completed"
        else -> "Draft"
    }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
            .border(1.dp, colorResource(R.color.stroke_soft), RoundedCornerShape(12.dp))
//            .clickable { onClick() } // Handle card click
        ,
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(Color.White)
    ) {
        // Same padding and spacing for both mobile and tablet
        val cardPadding = 16.dp
        val textSpacing = 8.dp

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(cardPadding),
            verticalArrangement = Arrangement.spacedBy(textSpacing)
        ) {
            // Header row with ID and more button
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "#${project.id}",
                    color = colorResource(R.color.text_sub),
                    fontSize = 14.sp
                )
                Icon(
                    painter = painterResource(id = R.drawable.ic_option_light),
                    contentDescription = "More",
                    modifier = Modifier
                        .size(16.dp)
                        .clickable { onMoreClick() }
                )
            }

            // Project name
            Text(
                text = project.name ?: "Unknown",
                color = colorResource(R.color.profit_black),
                fontSize = 14.sp,
                maxLines = 1,
                overflow = androidx.compose.ui.text.style.TextOverflow.Ellipsis
            )

            // Status and date row
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                StatusBadge(status = statusText)
                Text(
                    text = project.create_at,
                    color = colorResource(R.color.text_sub),
                    fontSize = 12.sp,
                    maxLines = 1
                )
            }
        }
    }
}


@Composable
fun BottomSheetContent(onOptionClick: (String) -> Unit) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        TextButton(onClick = { onOptionClick("Change Order") }, ) {
            Text("Change Order", color = Color.Black, fontSize = 16.sp)
        }

        TextButton(onClick = { onOptionClick("View Estimate") },) {
            Text("View Estimate", color = Color.Black, fontSize = 16.sp)
        }
    }
}

@Composable
fun ProjectBottomSheetHeader(
    onDismiss: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconButton(onClick = onDismiss) {
            Icon(Icons.Filled.Close, contentDescription = "Close")
        }
        Text(
            text = "Project Options",
            fontSize = 18.sp,
            fontWeight = FontWeight.Medium,
            color = Color.Black
        )
        // Empty space to balance the layout
        Spacer(modifier = Modifier.width(48.dp))
    }
}

@Composable
fun ProjectBottomSheetContentBody(
    onOptionClick: (String) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        TextButton(onClick = { onOptionClick("Change Order") }) {
            Text("Change Order", color = Color.Black, fontSize = 16.sp)
        }

        TextButton(onClick = { onOptionClick("View Estimate") }) {
            Text("View Estimate", color = Color.Black, fontSize = 16.sp)
        }
    }
}




// Status Badge Composable
@Composable
fun StatusBadge(status: String) {
    val statusColor = when (status) {
        "Active" -> colorResource(id = R.color.project_blue_bg) // Replace with your actual color resource
        "Completed" -> colorResource(id = R.color.project_green_bg)
        "Draft" -> colorResource(id = R.color.project_purple_bg)
        "Outstanding" -> colorResource(id = R.color.project_purple_bg)
        else -> Color.Gray
    }

    val statusTextColor = when (status) {
        "Active" -> colorResource(id = R.color.project_blue) // Replace with your actual color resource
        "Completed" -> colorResource(id = R.color.project_green)
        "Draft" -> colorResource(id = R.color.project_purple)
        "Outstanding" -> colorResource(id = R.color.project_purple)
        else -> Color.Gray
    }

    Box(
        modifier = Modifier
            .background(statusColor, shape = RoundedCornerShape(8.dp))
            .padding(horizontal = 8.dp, vertical = 4.dp)
    ) {
        Text(text = status, color = statusTextColor, fontSize = 12.sp, fontWeight = FontWeight.Medium)
    }
}


val mockProjectApiResponse = Resource.success(
    ProjectResponseModel(
        error = false,
        list = arrayListOf(
            list(
                name = "Project Alpha",
                change_count = 0,
                id = 604,
                customer_id = 66,
                user_id = 121,
                status = 1,
                profit_overhead = 20,
                hourly_rate = 20,
                create_at = "2025-04-02",
                update_at = "2025-04-02T16:18:59.000Z"
            ),
            list(
                name = "Beta Initiative",
                change_count = 0,
                id = 603,
                customer_id = 66,
                user_id = 121,
                status = 2,
                profit_overhead = 20,
                hourly_rate = 20,
                create_at = "2025-04-02",
                update_at = "2025-04-02T16:15:28.000Z"
            ),
            list(
                name = "Gamma Solutions",
                change_count = 0,
                id = 602,
                customer_id = 66,
                user_id = 121,
                status = 3,
                profit_overhead = 20,
                hourly_rate = 20,
                create_at = "2025-04-02",
                update_at = "2025-04-02T16:03:44.000Z"
            ),
            // ... (rest of your mock data)
        )
    )
)


@Preview(showBackground = true, widthDp = 360, heightDp = 640)
@Composable
fun ProjectsScreenMobilePreview() {
    val mockProjects = listOf(
        list(
            name = "Project Alpha",
            change_count = 0,
            id = 604,
            customer_id = 66,
            user_id = 121,
            status = 1,
            profit_overhead = 20,
            hourly_rate = 20,
            create_at = "2025-04-02",
            update_at = "2025-04-02T16:18:59.000Z"
        ),
        list(
            name = "Beta Initiative",
            change_count = 0,
            id = 603,
            customer_id = 66,
            user_id = 121,
            status = 2,
            profit_overhead = 20,
            hourly_rate = 20,
            create_at = "2025-04-02",
            update_at = "2025-04-02T16:15:28.000Z"
        )
    )

    ProjectList(
        projects = mockProjects,
        onProjectClick = { },
        onMoreClick = { }
    )
}

@Preview(showBackground = true, widthDp = 800, heightDp = 600, name = "Tablet Layout - Single Column")
@Composable
fun ProjectsScreenTabletPreview() {
    val mockProjects = listOf(
        list(
            name = "Project Alpha",
            change_count = 0,
            id = 604,
            customer_id = 66,
            user_id = 121,
            status = 1,
            profit_overhead = 20,
            hourly_rate = 20,
            create_at = "2025-04-02",
            update_at = "2025-04-02T16:18:59.000Z"
        ),
        list(
            name = "Beta Initiative",
            change_count = 0,
            id = 603,
            customer_id = 66,
            user_id = 121,
            status = 2,
            profit_overhead = 20,
            hourly_rate = 20,
            create_at = "2025-04-02",
            update_at = "2025-04-02T16:15:28.000Z"
        ),
        list(
            name = "Gamma Solutions",
            change_count = 0,
            id = 602,
            customer_id = 66,
            user_id = 121,
            status = 3,
            profit_overhead = 20,
            hourly_rate = 20,
            create_at = "2025-04-02",
            update_at = "2025-04-02T16:03:44.000Z"
        ),
        list(
            name = "Delta Project",
            change_count = 0,
            id = 601,
            customer_id = 66,
            user_id = 121,
            status = 4,
            profit_overhead = 20,
            hourly_rate = 20,
            create_at = "2025-04-02",
            update_at = "2025-04-02T16:03:44.000Z"
        )
    )

    ProjectList(
        projects = mockProjects,
        onProjectClick = { },
        onMoreClick = { }
    )
}
@Preview(showBackground = true)
@Composable
fun HeaderPreview() {
//    Header()
}

@Preview(showBackground = true)
@Composable
fun DropdownFilterPreview() {
    DropdownFilter("All")
}