<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AndroidLayouts">
    <shared>
      <config />
    </shared>
    <layouts>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout-sw600dp/fragment_accountview.xml">
        <config>
          <state>Portrait</state>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout-sw600dp/fragment_companysetup.xml">
        <config>
          <state>Portrait</state>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout-sw600dp/fragment_dashboardview.xml">
        <config>
          <state>Portrait</state>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout-sw600dp/fragment_home.xml">
        <config>
          <state>Portrait</state>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout-sw600dp/fragment_subscription.xml">
        <config>
          <state>Portrait</state>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_companysetup.xml">
        <config>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_create_estimation.xml">
        <config>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_dashboardview.xml">
        <config>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_home.xml">
        <config>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_profileview.xml">
        <config>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_subscription.xml">
        <config>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/header.xml">
        <config>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/item_dashboard_project.xml">
        <config>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/item_draw.xml">
        <config>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/navigation/mobile_navigation.xml">
        <config>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
    </layouts>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="7629a6d9-4a69-4249-9449-416164fda9ed" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/.gradle/8.5/executionHistory/executionHistory.bin" beforeDir="false" afterPath="$PROJECT_DIR$/.gradle/8.5/executionHistory/executionHistory.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gradle/8.5/executionHistory/executionHistory.lock" beforeDir="false" afterPath="$PROJECT_DIR$/.gradle/8.5/executionHistory/executionHistory.lock" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gradle/8.5/fileHashes/fileHashes.bin" beforeDir="false" afterPath="$PROJECT_DIR$/.gradle/8.5/fileHashes/fileHashes.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gradle/8.5/fileHashes/fileHashes.lock" beforeDir="false" afterPath="$PROJECT_DIR$/.gradle/8.5/fileHashes/fileHashes.lock" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gradle/8.5/fileHashes/resourceHashesCache.bin" beforeDir="false" afterPath="$PROJECT_DIR$/.gradle/8.5/fileHashes/resourceHashesCache.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gradle/buildOutputCleanup/buildOutputCleanup.lock" beforeDir="false" afterPath="$PROJECT_DIR$/.gradle/buildOutputCleanup/buildOutputCleanup.lock" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gradle/file-system.probe" beforeDir="false" afterPath="$PROJECT_DIR$/.gradle/file-system.probe" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/generated/data_binding_base_class_source_out/debug/out/Manaknight/databinding/BottomSheetMultiSelectStatusFilterBinding.java" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/generated/data_binding_base_class_source_out/debug/out/Manaknight/databinding/BottomSheetMultiSelectStatusFilterBinding.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/generated/data_binding_base_class_source_out/debug/out/Manaknight/databinding/FragmentHomeBinding.java" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/generated/data_binding_base_class_source_out/debug/out/Manaknight/databinding/FragmentHomeBinding.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/generated/data_binding_base_class_source_out/debug/out/Manaknight/databinding/ItemMultiSelectFilterOptionBinding.java" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/generated/data_binding_base_class_source_out/debug/out/Manaknight/databinding/ItemMultiSelectFilterOptionBinding.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/apk/debug/com.manaknight.app-1.0.0.apk" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/apk/debug/com.manaknight.app-1.0.0.apk" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/apk/debug/output-metadata.json" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/apk/debug/output-metadata.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/data_binding_base_class_log_artifact/debug/dataBindingGenBaseClassesDebug/out/Manaknight-binding_classes.json" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/data_binding_base_class_log_artifact/debug/dataBindingGenBaseClassesDebug/out/Manaknight-binding_classes.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/0/classes.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/0/classes.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/10/classes.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/10/classes.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/12/classes.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/12/classes.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/13/classes.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/13/classes.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/2/classes.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/2/classes.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/3/classes.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/3/classes.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/8/classes.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/8/classes.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/9/classes.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/9/classes.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/incremental/dataBindingGenBaseClassesDebug/base_builder_log.json" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/incremental/dataBindingGenBaseClassesDebug/base_builder_log.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/incremental/debug/mergeDebugResources/compile-file-map.properties" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/incremental/debug/mergeDebugResources/compile-file-map.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/incremental/debug/mergeDebugResources/merger.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/incremental/debug/mergeDebugResources/merger.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/incremental/debug/packageDebugResources/merger.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/incremental/debug/packageDebugResources/merger.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/incremental/packageDebug/tmp/debug/dex-renamer-state.txt" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/incremental/packageDebug/tmp/debug/dex-renamer-state.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/local_only_symbol_list/debug/parseDebugLocalResources/R-def.txt" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/local_only_symbol_list/debug/parseDebugLocalResources/R-def.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/manifest_merge_blame_file/debug/processDebugMainManifest/manifest-merger-blame-debug-report.txt" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/manifest_merge_blame_file/debug/processDebugMainManifest/manifest-merger-blame-debug-report.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/merged_manifest/debug/processDebugMainManifest/AndroidManifest.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/merged_manifest/debug/processDebugMainManifest/AndroidManifest.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/merged_manifests/debug/processDebugManifest/AndroidManifest.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/merged_manifests/debug/processDebugManifest/AndroidManifest.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/merged_res/debug/mergeDebugResources/values_values.arsc.flat" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/merged_res/debug/mergeDebugResources/values_values.arsc.flat" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/mergeDebugResources.json" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/mergeDebugResources.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values.json" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/multi-v2/values.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/single/layout-sw600dp-v13.json" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/single/layout-sw600dp-v13.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/single/layout.json" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/single/layout.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/single/mergeDebugResources.json" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/merged_res_blame_folder/debug/mergeDebugResources/out/single/mergeDebugResources.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/packaged_manifests/debug/processDebugManifestForPackage/AndroidManifest.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/packaged_manifests/debug/processDebugManifestForPackage/AndroidManifest.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/packaged_res/debug/packageDebugResources/values/values.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/packaged_res/debug/packageDebugResources/values/values.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt$lambda-11$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt$lambda-11$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt$lambda-12$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt$lambda-12$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt$lambda-13$1.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt$lambda-14$1.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt$lambda-2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt$lambda-2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt$lambda-3$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt$lambda-3$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt$lambda-4$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt$lambda-4$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt$lambda-5$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt$lambda-5$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ComposableSingletons$ProjectDetailsScreenKt.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$DrawItem$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$DrawItem$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$JobDetailsSheet$1$3.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$JobDetailsSheet$2.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$JobDetailsSheetContentBody$1$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$JobDetailsSheetContentBody$1$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$PaymentStatusBadge$1.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$PaymentStatusBadge$3.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1$1$4$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1$1$4$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1$1$4$1$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1$1$4$1$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1$1$4$1$3.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1$1$4$1$3.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$3.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$3.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$6.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$6.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/ProjectDetailsScreenKt.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/model/remote/profitPro/TeamMembers.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/model/remote/profitPro/TeamMembers.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/DashboardviewFragment$showBottomSheetDialog$composeView$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/DashboardviewFragment$showBottomSheetDialog$composeView$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/HomeFragment.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/HomeFragment.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/LinealSetupFragment.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/LinealSetupFragment.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/MaterialSetupFragment.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/MaterialSetupFragment.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/ProfieViewFragment.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/ProfieViewFragment.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/ProjectsFragment$onCreateView$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/ProjectsFragment$onCreateView$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/SquareSetupFragment.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/SquareSetupFragment.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/SubscriptionFragment.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/SubscriptionFragment.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/components/ResponsiveSheetContainerKt$ResponsiveSheetContainer$1$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/components/ResponsiveSheetContainerKt$ResponsiveSheetContainer$1$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/components/ResponsiveSheetContainerKt$ResponsiveSheetContainer$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/components/ResponsiveSheetContainerKt$ResponsiveSheetContainer$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/components/ResponsiveSheetContainerKt$ResponsiveSheetContainer$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/components/ResponsiveSheetContainerKt$ResponsiveSheetContainer$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/components/ResponsiveSheetContainerKt$ResponsiveSheetContainer$3.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/components/ResponsiveSheetContainerKt$ResponsiveSheetContainer$3.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/components/ResponsiveSheetContainerKt.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/components/ResponsiveSheetContainerKt.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/ProjectTrackingFragment$checkAndUpdateProjectStatus$1$2$WhenMappings.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/ProjectTrackingFragment$checkAndUpdateProjectStatus$1$2.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/ProjectTrackingFragment$checkAndUpdateProjectStatus$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/ProjectTrackingFragment$checkAndUpdateProjectStatus$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/ProjectTrackingFragment$handleMaterialEdit$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/ProjectTrackingFragment$handleMaterialEdit$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/ProjectTrackingFragment$handleMaterialSave$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/ProjectTrackingFragment$handleMaterialSave$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/ProjectTrackingFragment$handleUpdateDraws$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/ProjectTrackingFragment$handleUpdateDraws$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/ProjectTrackingFragment$onCreateView$1$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/ProjectTrackingFragment$onCreateView$1$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/ProjectTrackingFragment$onCreateView$1$1$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/ProjectTrackingFragment$onCreateView$1$1$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/ProjectTrackingFragment$onCreateView$1$1$3.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/ProjectTrackingFragment$onCreateView$1$1$3.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/ProjectTrackingFragment$onCreateView$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/ProjectTrackingFragment$onCreateView$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/ProjectTrackingFragment.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/ProjectTrackingFragment.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/ResetPasswordFragment.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/ResetPasswordFragment.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/SignUpFragment.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/SignUpFragment.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/SplashFragment.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/fragments/SplashFragment.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/AddEditMaterialLineItemScreenKt$AddEditMaterialLineItemDetailContent$1$3$1$3$1$invoke$$inlined$items$default$4.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/AddEditMaterialLineItemScreenKt$AddEditMaterialLineItemDetailContent$1$3$1$3$1$invoke$$inlined$items$default$4.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/AddEditMaterialLineItemScreenKt$AddEditMaterialLineItemDetailContent$1$5.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/AddEditMaterialLineItemScreenKt$AddEditMaterialLineItemDetailContent$1$5.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/AddEditMaterialLineItemScreenKt$AddEditMaterialLineItemScreen$3$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/AddEditMaterialLineItemScreenKt$AddEditMaterialLineItemScreen$3$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/AddEditMaterialLineItemScreenKt$AddEditMaterialLineItemScreen$3$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/AddEditMaterialLineItemScreenKt$AddEditMaterialLineItemScreen$3$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/AddEditMaterialLineItemScreenKt$AddEditMaterialLineItemScreen$4.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/AddEditMaterialLineItemScreenKt$AddEditMaterialLineItemScreen$4.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/AddEditMaterialLineItemScreenKt.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/AddEditMaterialLineItemScreenKt.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-10$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-10$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-11$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-11$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-12$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-12$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-13$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-13$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-14$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-14$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-3$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-3$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-4$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-4$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-5$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-5$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-6$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-6$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-7$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-7$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-8$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-8$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-9$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-9$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-10$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-10$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-11$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-11$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-12$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-12$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-13$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-13$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-14$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-14$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-15$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-15$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-16$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-16$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-17$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-17$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-18$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-18$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-19$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-19$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-20$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-20$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-21$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-21$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-22$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-22$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-23$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-23$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-24$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-24$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-25$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-25$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-26$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-26$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-27$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-27$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-28$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-28$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-29$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-29$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-3$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-3$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-30$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-30$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-31$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-31$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-32$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-32$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-33$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-33$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-4$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-4$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-5$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-5$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-6$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-6$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-7$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-7$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-8$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-8$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-9$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-9$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectsScreenKt$lambda-1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectsScreenKt$lambda-1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectsScreenKt$lambda-2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectsScreenKt$lambda-2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectsScreenKt$lambda-3$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectsScreenKt$lambda-3$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectsScreenKt$lambda-4$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectsScreenKt$lambda-4$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectsScreenKt$lambda-5$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectsScreenKt$lambda-5$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectsScreenKt$lambda-6$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectsScreenKt$lambda-6$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectsScreenKt.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ComposableSingletons$ProjectsScreenKt.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/CostsScreenKt$CostScreen$11$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/CostsScreenKt$CostScreen$11$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/CostsScreenKt.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/CostsScreenKt.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/EditLineItemScreenKt$EditLineItemDetailContent$1$2$2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/EditLineItemScreenKt$EditLineItemDetailContent$1$2$2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/EditLineItemScreenKt$EditLineItemScreen$2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/EditLineItemScreenKt$EditLineItemScreen$2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/EditLineItemScreenKt$EditLineItemScreen$2$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/EditLineItemScreenKt$EditLineItemScreen$2$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/EditLineItemScreenKt$EditLineItemScreen$3.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/EditLineItemScreenKt$EditLineItemScreen$3.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/EditLineItemScreenKt.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/EditLineItemScreenKt.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/JobDetailsUIModel.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/JobDetailsUIModel.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/LineItemsScreenKt$CustomCreditSheetContent$1$1$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/LineItemsScreenKt$CustomCreditSheetContent$1$1$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/LineItemsScreenKt$CustomCreditSheetContent$1$2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/LineItemsScreenKt$CustomCreditSheetContent$1$2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/LineItemsScreenKt$CustomCreditSheetContent$1$3$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/LineItemsScreenKt$CustomCreditSheetContent$1$3$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/LineItemsScreenKt$CustomCreditSheetContentBody$1$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/LineItemsScreenKt$CustomCreditSheetContentBody$1$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/LineItemsScreenKt$CustomCreditSheetContentBody$1$2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/LineItemsScreenKt$CustomCreditSheetContentBody$1$2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/LineItemsScreenKt$CustomCreditSheetHeader$1$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/LineItemsScreenKt$CustomCreditSheetHeader$1$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/LineItemsScreenKt$LineItemsScreen$3$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/LineItemsScreenKt$LineItemsScreen$3$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/LineItemsScreenKt$LineItemsScreen$3$2$1$1$1$2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/LineItemsScreenKt$LineItemsScreen$3$2$1$1$1$2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/LineItemsScreenKt$LineItemsScreen$3$2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/LineItemsScreenKt$LineItemsScreen$3$2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/LineItemsScreenKt$LineItemsScreen$3$2$3.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/LineItemsScreenKt$LineItemsScreen$3$2$3.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/LineItemsScreenKt$LineItemsScreen$3$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/LineItemsScreenKt$LineItemsScreen$3$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/LineItemsScreenKt.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/LineItemsScreenKt.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddMaterialExpenseSheet$1$1$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddMaterialExpenseSheet$1$1$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddMaterialExpenseSheet$1$2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddMaterialExpenseSheet$1$2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddMaterialExpenseSheet$1$3$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddMaterialExpenseSheet$1$3$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddMaterialExpenseSheetHeader$1$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddMaterialExpenseSheetHeader$1$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheet$1$1$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheet$1$1$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheet$1$1$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheet$1$1$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheet$1$2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheet$1$2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheet$1$3$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheet$1$3$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheet$1$4$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheet$1$4$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheet$1$4$2$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheet$1$4$2$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheet$1$4$3$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheet$1$4$3$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheet$1$5$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheet$1$5$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheet$1$6$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheet$1$6$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$DrawsContent$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$DrawsContent$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$DrawsContent$2$1$2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$DrawsContent$2$1$2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$DrawsContent$2$1$3$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$DrawsContent$2$1$3$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$DrawsDropdownFilter$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$DrawsDropdownFilter$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$DrawsDropdownFilter$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$DrawsDropdownFilter$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$DrawsStatusFilterDropdown$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$DrawsStatusFilterDropdown$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditLaborSheet$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditLaborSheet$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditLaborSheet$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditLaborSheet$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditLaborSheetContentBody$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditLaborSheetContentBody$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditLaborSheetHeader$1$1.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditLaborSheetHeader$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditLaborSheetHeader$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditMaterialExpenseSheet$1$1$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditMaterialExpenseSheet$1$1$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditMaterialExpenseSheet$1$2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditMaterialExpenseSheet$1$2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditMaterialExpenseSheet$1$3$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditMaterialExpenseSheet$1$3$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditMaterialExpenseSheetHeader$1$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditMaterialExpenseSheetHeader$1$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$InvoiceOptionsSheet$1$2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$InvoiceOptionsSheet$1$2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$InvoiceOptionsSheet$1$4$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$InvoiceOptionsSheet$1$4$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$LaborContent$2.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$LaborCostItem$1$4$1$1.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$LaborCostItem$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$LaborCostItem$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ListItem$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ListItem$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ListItem$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ListItem$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ListItem$3$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ListItem$3$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ListItem$3$2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ListItem$3$2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ListItem$4.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ListItem$4.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialItem$1$3$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialItem$1$3$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$2$1$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$2$1$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$2$2$1$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$2$2$1$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$2$2$invoke$$inlined$items$default$4.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$2$2$invoke$$inlined$items$default$4.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$2$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$2$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$3$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$3$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$4$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$4$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$4$2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$4$2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$4.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$4.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$5$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$5$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$5$2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$5$2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$5.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$5.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$1$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$1$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$10$1.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$11$1$1.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$11$2$1.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$11.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$12$1.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$13$1$1.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$13$2.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$13$3$1.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$13.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$1$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$1$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$1$2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$1$2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$1$3$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$1$3$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$2$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$2$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$2$2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$2$2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$2$3$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$2$3$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$2$4$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$2$4$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$2$5$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$2$5$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$2$5$2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$2$5$2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$2$6$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$2$6$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$2$7$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$2$7$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$2$8$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$2$8$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$3$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$3$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$4$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$4$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$4$2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$4$2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$4.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$4.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$5$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$5$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$5$2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$5$2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$5.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$5.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$6$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$6$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$7$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$7$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$7.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$7.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$8$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$8$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$9$1$1.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$9$2$1.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$9.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$4.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$4.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$TabButton$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$TabButton$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectTrackingScreenKt.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$BottomSheetContent$1$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$BottomSheetContent$1$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$BottomSheetContent$1$2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$BottomSheetContent$1$2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$DropdownFilter$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$DropdownFilter$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$MonthFilterDropdown$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$MonthFilterDropdown$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$MonthFilterSheet$2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$MonthFilterSheet$2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$MonthlyFilterSheet$2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$MonthlyFilterSheet$2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$MultiSelectStatusFilterSheet$2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$MultiSelectStatusFilterSheet$2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectBottomSheetContentBody$1$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectBottomSheetContentBody$1$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectBottomSheetContentBody$1$2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectBottomSheetContentBody$1$2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectCard$1$1.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectCard$2$1$1$1$1.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectCard$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectCard$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectCard$3.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectList$1$1$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectList$1$1$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectList$1$1$2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectList$1$1$2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectList$1$invoke$$inlined$items$default$4.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectList$1$invoke$$inlined$items$default$4.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectList$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectList$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectListContent$1$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectListContent$1$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectListContent$1$3$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectListContent$1$3$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectListContent$1$3.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectListContent$1$3.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectListContent$1$4$1$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectListContent$1$4$1$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectListContent$1$5$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectListContent$1$5$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectsScreen$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectsScreen$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectsScreenMobilePreview$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectsScreenMobilePreview$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectsScreenMobilePreview$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectsScreenMobilePreview$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectsScreenTabletPreview$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectsScreenTabletPreview$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectsScreenTabletPreview$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectsScreenTabletPreview$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$StatusFilterDropdown$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt$StatusFilterDropdown$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/ProjectsScreenKt.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/TeamScreenKt$TeamScreen$8$3.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/TeamScreenKt$TeamScreen$8$3.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/TeamScreenKt.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/ui/screens/TeamScreenKt.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/utils/SubscriptionManager.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/utils/SubscriptionManager.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/widget/SimpleChatView.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/manaknight/app/widget/SimpleChatView.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/runtime_symbol_list/debug/processDebugResources/R.txt" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/runtime_symbol_list/debug/processDebugResources/R.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/stable_resource_ids_file/debug/processDebugResources/stableIds.txt" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/stable_resource_ids_file/debug/processDebugResources/stableIds.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/symbol_list_with_package_name/debug/processDebugResources/package-aware-r.txt" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/symbol_list_with_package_name/debug/processDebugResources/package-aware-r.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values.s" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values.s" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.s" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.s" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.values" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.values" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.values.s" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.values.s" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values.s" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values.s" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.values" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.values" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.values.s" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.values.s" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/counters.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/counters.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab_i" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab_i" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.values" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.values" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.values.s" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.values.s" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab_i" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab_i" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/last-build.bin" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/last-build.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/local-state/build-history.bin" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/local-state/build-history.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values.s" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values.s" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.s" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.s" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values.s" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values.s" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/counters.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/counters.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab_i" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab_i" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.values" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.values" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.values.s" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.values.s" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab_i" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab_i" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/last-build.bin" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/cacheable/last-build.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/local-state/build-history.bin" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/kaptGenerateStubsDebugKotlin/local-state/build-history.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/incApCache/debug/java-cache.bin" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/incApCache/debug/java-cache.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/incrementalData/debug/ProjectDetailsScreenKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/incrementalData/debug/ProjectDetailsScreenKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/incrementalData/debug/com/manaknight/app/model/remote/profitPro/TeamMembers.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/incrementalData/debug/com/manaknight/app/model/remote/profitPro/TeamMembers.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/incrementalData/debug/com/manaknight/app/ui/components/ResponsiveSheetContainerKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/incrementalData/debug/com/manaknight/app/ui/components/ResponsiveSheetContainerKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/incrementalData/debug/com/manaknight/app/ui/fragments/ProjectTrackingFragment.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/incrementalData/debug/com/manaknight/app/ui/fragments/ProjectTrackingFragment.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/incrementalData/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/incrementalData/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/ProjectDetailsScreenKt.java" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/ProjectDetailsScreenKt.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/ProjectDetailsScreenKt.kapt_metadata" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/ProjectDetailsScreenKt.kapt_metadata" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/model/remote/profitPro/TeamMembers.java" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/model/remote/profitPro/TeamMembers.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/model/remote/profitPro/TeamMembers.kapt_metadata" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/model/remote/profitPro/TeamMembers.kapt_metadata" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/components/ResponsiveSheetContainerKt.java" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/components/ResponsiveSheetContainerKt.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/components/ResponsiveSheetContainerKt.kapt_metadata" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/components/ResponsiveSheetContainerKt.kapt_metadata" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/fragments/ProjectTrackingFragment.java" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/fragments/ProjectTrackingFragment.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/fragments/ProjectTrackingFragment.kapt_metadata" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/fragments/ProjectTrackingFragment.kapt_metadata" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/screens/LineItemsScreenKt.kapt_metadata" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/screens/LineItemsScreenKt.kapt_metadata" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt.java" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt.kapt_metadata" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt.kapt_metadata" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/screens/ProjectsScreenKt.kapt_metadata" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kapt3/stubs/debug/com/manaknight/app/ui/screens/ProjectsScreenKt.kapt_metadata" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt$lambda-11$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt$lambda-11$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt$lambda-12$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt$lambda-12$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt$lambda-13$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt$lambda-14$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt$lambda-2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt$lambda-2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt$lambda-3$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt$lambda-3$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt$lambda-4$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt$lambda-4$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt$lambda-5$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt$lambda-5$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ComposableSingletons$ProjectDetailsScreenKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$DrawItem$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$DrawItem$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$JobDetailsSheet$1$3.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$JobDetailsSheet$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$JobDetailsSheetContentBody$1$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$JobDetailsSheetContentBody$1$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$PaymentStatusBadge$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$PaymentStatusBadge$3.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1$1$4$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1$1$4$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1$1$4$1$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1$1$4$1$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1$1$4$1$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1$1$4$1$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$6.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt$ProjectDetailsScreen$2$1$6.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/ProjectDetailsScreenKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/model/remote/profitPro/TeamMembers.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/model/remote/profitPro/TeamMembers.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/DashboardviewFragment$showBottomSheetDialog$composeView$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/DashboardviewFragment$showBottomSheetDialog$composeView$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/ProjectsFragment$onCreateView$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/ProjectsFragment$onCreateView$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/components/ResponsiveSheetContainerKt$ResponsiveSheetContainer$1$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/components/ResponsiveSheetContainerKt$ResponsiveSheetContainer$1$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/components/ResponsiveSheetContainerKt$ResponsiveSheetContainer$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/components/ResponsiveSheetContainerKt$ResponsiveSheetContainer$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/components/ResponsiveSheetContainerKt$ResponsiveSheetContainer$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/components/ResponsiveSheetContainerKt$ResponsiveSheetContainer$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/components/ResponsiveSheetContainerKt$ResponsiveSheetContainer$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/components/ResponsiveSheetContainerKt$ResponsiveSheetContainer$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/components/ResponsiveSheetContainerKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/components/ResponsiveSheetContainerKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/ProjectTrackingFragment$checkAndUpdateProjectStatus$1$2$WhenMappings.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/ProjectTrackingFragment$checkAndUpdateProjectStatus$1$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/ProjectTrackingFragment$checkAndUpdateProjectStatus$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/ProjectTrackingFragment$checkAndUpdateProjectStatus$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/ProjectTrackingFragment$handleMaterialEdit$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/ProjectTrackingFragment$handleMaterialEdit$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/ProjectTrackingFragment$handleMaterialSave$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/ProjectTrackingFragment$handleMaterialSave$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/ProjectTrackingFragment$handleUpdateDraws$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/ProjectTrackingFragment$handleUpdateDraws$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/ProjectTrackingFragment$onCreateView$1$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/ProjectTrackingFragment$onCreateView$1$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/ProjectTrackingFragment$onCreateView$1$1$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/ProjectTrackingFragment$onCreateView$1$1$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/ProjectTrackingFragment$onCreateView$1$1$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/ProjectTrackingFragment$onCreateView$1$1$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/ProjectTrackingFragment$onCreateView$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/ProjectTrackingFragment$onCreateView$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/ProjectTrackingFragment.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/fragments/ProjectTrackingFragment.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/AddEditMaterialLineItemScreenKt$AddEditMaterialLineItemDetailContent$1$3$1$3$1$invoke$$inlined$items$default$4.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/AddEditMaterialLineItemScreenKt$AddEditMaterialLineItemDetailContent$1$3$1$3$1$invoke$$inlined$items$default$4.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/AddEditMaterialLineItemScreenKt$AddEditMaterialLineItemDetailContent$1$5.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/AddEditMaterialLineItemScreenKt$AddEditMaterialLineItemDetailContent$1$5.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/AddEditMaterialLineItemScreenKt$AddEditMaterialLineItemScreen$3$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/AddEditMaterialLineItemScreenKt$AddEditMaterialLineItemScreen$3$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/AddEditMaterialLineItemScreenKt$AddEditMaterialLineItemScreen$3$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/AddEditMaterialLineItemScreenKt$AddEditMaterialLineItemScreen$3$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/AddEditMaterialLineItemScreenKt$AddEditMaterialLineItemScreen$4.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/AddEditMaterialLineItemScreenKt$AddEditMaterialLineItemScreen$4.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/AddEditMaterialLineItemScreenKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/AddEditMaterialLineItemScreenKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-10$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-10$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-11$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-11$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-12$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-12$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-13$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-13$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-14$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-14$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-3$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-3$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-4$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-4$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-5$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-5$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-6$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-6$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-7$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-7$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-8$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-8$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-9$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt$lambda-9$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$LineItemsScreenKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-10$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-10$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-11$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-11$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-12$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-12$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-13$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-13$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-14$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-14$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-15$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-15$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-16$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-16$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-17$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-17$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-18$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-18$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-19$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-19$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-20$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-20$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-21$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-21$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-22$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-22$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-23$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-23$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-24$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-24$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-25$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-25$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-26$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-26$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-27$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-27$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-28$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-28$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-29$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-29$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-3$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-3$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-30$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-30$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-31$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-31$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-32$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-32$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-33$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-33$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-4$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-4$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-5$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-5$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-6$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-6$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-7$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-7$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-8$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-8$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-9$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt$lambda-9$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectTrackingScreenKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectsScreenKt$lambda-1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectsScreenKt$lambda-1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectsScreenKt$lambda-2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectsScreenKt$lambda-2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectsScreenKt$lambda-3$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectsScreenKt$lambda-3$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectsScreenKt$lambda-4$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectsScreenKt$lambda-4$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectsScreenKt$lambda-5$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectsScreenKt$lambda-5$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectsScreenKt$lambda-6$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectsScreenKt$lambda-6$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectsScreenKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ComposableSingletons$ProjectsScreenKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/CostsScreenKt$CostScreen$11$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/CostsScreenKt$CostScreen$11$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/CostsScreenKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/CostsScreenKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/EditLineItemScreenKt$EditLineItemDetailContent$1$2$2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/EditLineItemScreenKt$EditLineItemDetailContent$1$2$2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/EditLineItemScreenKt$EditLineItemScreen$2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/EditLineItemScreenKt$EditLineItemScreen$2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/EditLineItemScreenKt$EditLineItemScreen$2$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/EditLineItemScreenKt$EditLineItemScreen$2$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/EditLineItemScreenKt$EditLineItemScreen$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/EditLineItemScreenKt$EditLineItemScreen$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/EditLineItemScreenKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/EditLineItemScreenKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/JobDetailsUIModel.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/JobDetailsUIModel.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/LineItemsScreenKt$CustomCreditSheetContent$1$1$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/LineItemsScreenKt$CustomCreditSheetContent$1$1$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/LineItemsScreenKt$CustomCreditSheetContent$1$2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/LineItemsScreenKt$CustomCreditSheetContent$1$2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/LineItemsScreenKt$CustomCreditSheetContent$1$3$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/LineItemsScreenKt$CustomCreditSheetContent$1$3$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/LineItemsScreenKt$CustomCreditSheetContentBody$1$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/LineItemsScreenKt$CustomCreditSheetContentBody$1$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/LineItemsScreenKt$CustomCreditSheetContentBody$1$2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/LineItemsScreenKt$CustomCreditSheetContentBody$1$2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/LineItemsScreenKt$CustomCreditSheetHeader$1$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/LineItemsScreenKt$CustomCreditSheetHeader$1$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/LineItemsScreenKt$LineItemsScreen$3$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/LineItemsScreenKt$LineItemsScreen$3$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/LineItemsScreenKt$LineItemsScreen$3$2$1$1$1$2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/LineItemsScreenKt$LineItemsScreen$3$2$1$1$1$2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/LineItemsScreenKt$LineItemsScreen$3$2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/LineItemsScreenKt$LineItemsScreen$3$2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/LineItemsScreenKt$LineItemsScreen$3$2$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/LineItemsScreenKt$LineItemsScreen$3$2$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/LineItemsScreenKt$LineItemsScreen$3$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/LineItemsScreenKt$LineItemsScreen$3$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/LineItemsScreenKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/LineItemsScreenKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddMaterialExpenseSheet$1$1$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddMaterialExpenseSheet$1$1$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddMaterialExpenseSheet$1$2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddMaterialExpenseSheet$1$2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddMaterialExpenseSheet$1$3$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddMaterialExpenseSheet$1$3$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddMaterialExpenseSheetHeader$1$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddMaterialExpenseSheetHeader$1$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheet$1$1$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheet$1$1$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheet$1$1$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheet$1$1$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheet$1$2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheet$1$2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheet$1$3$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheet$1$3$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheet$1$4$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheet$1$4$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheet$1$4$2$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheet$1$4$2$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheet$1$4$3$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheet$1$4$3$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheet$1$5$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheet$1$5$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheet$1$6$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$AddPaymentMethodSheet$1$6$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$DrawsContent$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$DrawsContent$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$DrawsContent$2$1$2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$DrawsContent$2$1$2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$DrawsContent$2$1$3$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$DrawsContent$2$1$3$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$DrawsDropdownFilter$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$DrawsDropdownFilter$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$DrawsDropdownFilter$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$DrawsDropdownFilter$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$DrawsStatusFilterDropdown$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$DrawsStatusFilterDropdown$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditLaborSheet$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditLaborSheet$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditLaborSheet$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditLaborSheet$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditLaborSheetContentBody$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditLaborSheetContentBody$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditLaborSheetHeader$1$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditLaborSheetHeader$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditLaborSheetHeader$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditMaterialExpenseSheet$1$1$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditMaterialExpenseSheet$1$1$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditMaterialExpenseSheet$1$2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditMaterialExpenseSheet$1$2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditMaterialExpenseSheet$1$3$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditMaterialExpenseSheet$1$3$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditMaterialExpenseSheetHeader$1$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$EditMaterialExpenseSheetHeader$1$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$InvoiceOptionsSheet$1$2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$InvoiceOptionsSheet$1$2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$InvoiceOptionsSheet$1$4$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$InvoiceOptionsSheet$1$4$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$LaborContent$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$LaborCostItem$1$4$1$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$LaborCostItem$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$LaborCostItem$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ListItem$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ListItem$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ListItem$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ListItem$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ListItem$3$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ListItem$3$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ListItem$3$2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ListItem$3$2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ListItem$4.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ListItem$4.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialItem$1$3$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialItem$1$3$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$2$1$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$2$1$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$2$2$1$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$2$2$1$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$2$2$invoke$$inlined$items$default$4.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$2$2$invoke$$inlined$items$default$4.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$2$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$2$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$3$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$3$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$4$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$4$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$4$2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$4$2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$4.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$4.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$5$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$5$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$5$2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$5$2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$5.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$MaterialsContent$1$5.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$1$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$1$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$10$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$11$1$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$11$2$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$11.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$12$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$13$1$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$13$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$13$3$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$13.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$1$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$1$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$1$2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$1$2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$1$3$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$1$3$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$2$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$2$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$2$2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$2$2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$2$3$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$2$3$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$2$4$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$2$4$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$2$5$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$2$5$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$2$5$2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$2$5$2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$2$6$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$2$6$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$2$7$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$2$7$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$2$8$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$2$1$2$8$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$3$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$3$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$4$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$4$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$4$2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$4$2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$4.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$4.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$5$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$5$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$5$2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$5$2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$5.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$5.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$6$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$6$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$7$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$7$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$7.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$7.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$8$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$8$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$9$1$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$9$2$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3$1$9.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$4.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$ProjectTrackingScreen$4.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$TabButton$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt$TabButton$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectTrackingScreenKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$BottomSheetContent$1$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$BottomSheetContent$1$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$BottomSheetContent$1$2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$BottomSheetContent$1$2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$DropdownFilter$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$DropdownFilter$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$MonthFilterDropdown$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$MonthFilterDropdown$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$MonthFilterSheet$2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$MonthFilterSheet$2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$MonthlyFilterSheet$2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$MonthlyFilterSheet$2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$MultiSelectStatusFilterSheet$2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$MultiSelectStatusFilterSheet$2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectBottomSheetContentBody$1$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectBottomSheetContentBody$1$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectBottomSheetContentBody$1$2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectBottomSheetContentBody$1$2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectCard$1$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectCard$2$1$1$1$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectCard$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectCard$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectCard$3.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectList$1$1$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectList$1$1$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectList$1$1$2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectList$1$1$2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectList$1$invoke$$inlined$items$default$4.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectList$1$invoke$$inlined$items$default$4.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectList$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectList$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectListContent$1$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectListContent$1$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectListContent$1$3$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectListContent$1$3$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectListContent$1$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectListContent$1$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectListContent$1$4$1$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectListContent$1$4$1$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectListContent$1$5$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectListContent$1$5$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectsScreen$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectsScreen$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectsScreenMobilePreview$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectsScreenMobilePreview$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectsScreenMobilePreview$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectsScreenMobilePreview$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectsScreenTabletPreview$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectsScreenTabletPreview$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectsScreenTabletPreview$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$ProjectsScreenTabletPreview$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$StatusFilterDropdown$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt$StatusFilterDropdown$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/ProjectsScreenKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/TeamScreenKt$TeamScreen$8$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/TeamScreenKt$TeamScreen$8$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/TeamScreenKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/manaknight/app/ui/screens/TeamScreenKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/model/remote/profitPro/ProjectTrackingResponse.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/model/remote/profitPro/ProjectTrackingResponse.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/components/ResponsiveSheetContainer.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/components/ResponsiveSheetContainer.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/fragments/ProjectTrackingFragment.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/fragments/ProjectTrackingFragment.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/screens/ProjectDetailsScreen.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/screens/ProjectDetailsScreen.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/screens/ProjectTrackingScreen.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/screens/ProjectTrackingScreen.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/screens/ProjectsScreen.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/screens/ProjectsScreen.kt" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="device_and_snapshot_combo_box_target[DeviceId(pluginId=Default, isTemplate=false, identifier=serial=192.168.113.106:5555;connection=acd2466f)]" />
  <component name="ExternalProjectsData">
    <projectState path="$PROJECT_DIR$">
      <ProjectState />
    </projectState>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2yvLLqUWRHbky1ztneXbIyjFVNC" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Android App.app.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.cidr.known.project.marker": "true",
    "RunOnceActivity.readMode.enableVisualFormatting": "true",
    "cf.first.check.clang-format": "false",
    "cidr.known.project.marker": "true",
    "git-widget-placeholder": "changes",
    "ignore.virus.scanning.warn.message": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "C:/Users/<USER>/AndroidStudioProjects/luv_android",
    "project.structure.last.edited": "SDK Location",
    "project.structure.proportion": "0.17",
    "project.structure.side.proportion": "0.2",
    "settings.editor.selected.configurable": "reference.settingsdialog.project.gradle"
  }
}]]></component>
  <component name="PsdUISettings">
    <option name="LAST_EDITED_BUILD_TYPE" value="release" />
  </component>
  <component name="RunManager">
    <configuration name="app" type="AndroidRunConfigurationType" factoryName="Android App" activateToolWindowBeforeRun="false">
      <module name="profitpro.app" />
      <option name="ANDROID_RUN_CONFIGURATION_SCHEMA_VERSION" value="1" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="ALLOW_ASSUME_VERIFIED" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="RESTORE_ENABLED" value="false" />
      <option name="RESTORE_FILE" value="" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Auto" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="7629a6d9-4a69-4249-9449-416164fda9ed" name="Changes" comment="" />
      <created>1750710046701</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750710046701</updated>
    </task>
    <servers />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="kotlin-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/screens/ProjectTrackingScreen.kt</url>
          <line>834</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="kotlin-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/screens/ProjectDetailsScreen.kt</url>
          <line>662</line>
          <option name="timeStamp" value="3" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="kotlin-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/screens/ProjectTrackingScreen.kt</url>
          <line>632</line>
          <option name="timeStamp" value="6" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="kotlin-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/screens/ProjectTrackingScreen.kt</url>
          <line>1082</line>
          <option name="timeStamp" value="7" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="compose-function">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/manaknight/app/ui/screens/ProjectTrackingScreen.kt</url>
          <line>566</line>
          <properties method="LaborContent">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="5" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="play_dynamic_filters_status">
    <option name="appIdToCheckInfo">
      <map>
        <entry key="com.manaknight.app">
          <value>
            <CheckInfo lastCheckTimestamp="1751388528019" />
          </value>
        </entry>
        <entry key="com.manaknight.app.test">
          <value>
            <CheckInfo lastCheckTimestamp="1751388528019" />
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>