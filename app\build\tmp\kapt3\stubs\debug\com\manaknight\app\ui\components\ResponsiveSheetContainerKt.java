package com.manaknight.app.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000.\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0000\u001a`\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\u0006\u0010\u0006\u001a\u00020\u00072\u0011\u0010\b\u001a\r\u0012\u0004\u0012\u00020\u00010\u0005\u00a2\u0006\u0002\b\t2\u0011\u0010\n\u001a\r\u0012\u0004\u0012\u00020\u00010\u0005\u00a2\u0006\u0002\b\t2\b\b\u0002\u0010\u000b\u001a\u00020\f2\b\b\u0002\u0010\r\u001a\u00020\u000eH\u0007\u00a8\u0006\u000f"}, d2 = {"ResponsiveSheetContainer", "", "showSheet", "", "onDismiss", "Lkotlin/Function0;", "sheetState", "Landroidx/compose/material3/SheetState;", "headerContent", "Landroidx/compose/runtime/Composable;", "content", "modifier", "Landroidx/compose/ui/Modifier;", "fraction", "", "app_debug"})
public final class ResponsiveSheetContainerKt {
    
    /**
     * A responsive container that shows bottom sheets on phones but displays them as centered dialogs on tablets.
     *
     * On tablets (≥600dp width):
     * - Shows a centered dialog with 600dp width
     * - Header content spans full width (600dp)
     * - Main content is constrained to 500dp width
     *
     * On phones:
     * - Shows a standard bottom sheet
     * - All content uses full width
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void ResponsiveSheetContainer(boolean showSheet, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    androidx.compose.material3.SheetState sheetState, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> headerContent, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> content, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, float fraction) {
    }
}