package com.manaknight.app.ui.fragments
import android.app.Dialog
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.compose.ui.platform.ComposeView
import androidx.navigation.fragment.findNavController
//import androidx.fragment.app.viewModels
//import com.manaknight.app.viewmodels.BaasViewModel
import org.koin.androidx.viewmodel.ext.android.viewModel
import com.manaknight.app.ui.screens.ProjectTrackingScreen
import com.manaknight.app.utils.ProgressDialog.Companion.progressDialog
import com.manaknight.app.viewmodels.BaasViewModel
import com.manaknight.app.network.Status


class ProjectTrackingFragment:Fragment() {
    private val baasViewModel: BaasViewModel by viewModel()  // Use viewModels() here
    private lateinit var dialog: Dialog
    private var hasUpdatedProjectStatus = false // Flag to prevent multiple updates
    // Initialize with -1 or 0 depending on your status range
    private var lastUpdatedProjectStatus: Int = -1

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val projectId = arguments?.getInt("projectId") ?: 0 // Retrieve the argument
        val projectName = arguments?.getString("customerName") ?: "" // Retrieve the argument
        dialog = progressDialog(requireContext())
        return ComposeView(requireContext()).apply {
            setContent {
                ProjectTrackingScreen(
                    projectId = projectId,
                    projectName = projectName,
                    baasViewModel = baasViewModel,
                    dialog = dialog,
                    navController = findNavController(),
                    onSaveMaterial = { materialName, unitCost ->
                        handleMaterialSave(materialName, unitCost)
                    },
                    onEditMaterial = { materialName: String, unitCost: String, id: Int ->
                        handleMaterialEdit(materialName, unitCost, id)
                    },
                    onUpdateDraws = { paymentMethodId: Int, checkNumber: String, drawItem ->
                        handleUpdateDraws(paymentMethodId, checkNumber, drawItem)
                    }
                ) // Pass the scoped ViewModel
            }
        }
    }

    private fun handleMaterialSave(materialName: String, unitCost: String) {
        val projectId = arguments?.getInt("projectId") ?: 0 // Retrieve the argument
        baasViewModel.createMaterial(
            name = materialName,
            unitCost = unitCost.toInt(),
            projectId = projectId.toString()
        ).observe(viewLifecycleOwner) { result ->
            when (result.status) {
                Status.LOADING -> dialog.show()
                Status.SUCCESS -> {
                    dialog.dismiss()
                    if (result.data?.error == true) {
                        Log.d("TAG", "handleMaterialSave: error")
                    } else {
                        // Success with no error
                        // ✅ Material successfully created → Refetch tracking data
                        baasViewModel.fetchProjectTrackingData(projectId)
                    }
                }

                Status.ERROR -> {
                    dialog.dismiss()
//                    snackBarForDialog("An unexpected error occurred.")
                }
            }
        }
    }
    private fun handleMaterialEdit(materialName: String, unitCost: String, id: Int) {
        val projectId = arguments?.getInt("projectId") ?: 0 // Retrieve the argument
        baasViewModel.updateMaterial(
            name = materialName,
            unitCost = unitCost.toInt(),
            projectId = projectId.toString(),
            id = id
        ).observe(viewLifecycleOwner) { result ->
            when (result.status) {
                Status.LOADING -> dialog.show()
                Status.SUCCESS -> {
                    dialog.dismiss()
                    if (result.data?.error == true) {
                        Log.d("TAG", "handleMaterialEdit: error")
                    } else {
                        // Success with no error
                        // ✅ Material successfully updated → Refetch tracking data
                        baasViewModel.fetchProjectTrackingData(projectId)
                    }
                }

                Status.ERROR -> {
                    dialog.dismiss()
//                    snackBarForDialog("An unexpected error occurred.")
                }
            }
        }
    }

    private fun handleUpdateDraws(paymentMethodId: Int, checkNumber: String, drawItem: com.manaknight.app.model.remote.profitPro.DrawItem) {
        val projectId = arguments?.getInt("projectId") ?: 0 // Retrieve the argument

        // Map payment method ID to string for API
        val paymentMethodString = when (paymentMethodId) {
            0 -> "0"
            1 -> "1"
            2 -> "2"
            else -> "0" // Default fallback
        }

        baasViewModel.updateDraws(
            checkNo = if (paymentMethodId == 1) checkNumber else null, // Only set check number for Check (1)
            amount = drawItem.amount, // Keep existing amount
            percentage = drawItem.percentage, // Keep existing percentage
            description = drawItem.description, // Keep existing description
            status = 1, // Keep existing status
            projectId = projectId,
            paymentType = paymentMethodString,
            id = drawItem.id
        ).observe(viewLifecycleOwner) { result ->
            when (result.status) {
                Status.LOADING -> dialog.show()
                Status.SUCCESS -> {
                    dialog.dismiss()
                    if (result.data?.error == true) {
                        Log.d("TAG", "handleUpdateDraws: error - ${result.data.message}")
                    } else {
                        // Success with no error
                        // ✅ Draw successfully updated → Refetch tracking data
                        baasViewModel.fetchProjectTrackingData(projectId)
                        Log.d("TAG", "Payment method updated successfully")

                        // Check if all draws now have status 1, then update project status to 4
                        checkAndUpdateProjectStatus(projectId)
                    }
                }

                Status.ERROR -> {
                    dialog.dismiss()
                    Log.d("TAG", "handleUpdateDraws: error - ${result.message}")
//                    snackBarForDialog("An unexpected error occurred.")
                }
            }
        }
    }



    private fun checkAndUpdateProjectStatus(projectId: Int) {
        baasViewModel.trackingResponse.observe(viewLifecycleOwner) { resource ->
            if (resource?.status == Status.SUCCESS && resource.data?.error == false) {
                val drawsList = resource.data.trackingData?.draws?.list ?: emptyList()

                if (drawsList.isNotEmpty()) {
                    val allDrawsAreComplete = drawsList.all { it.status?.toString() == "1" }
                    val anyDrawIsStarted = drawsList.any { it.status?.toString() == "1" }

                    val newStatus = when {
                        allDrawsAreComplete -> 4 // Completed
                        anyDrawIsStarted -> 1     // Started/In Progress
                        else -> null              // No update
                    }

                    if (newStatus != null) {
                        // Only update if newStatus is higher than lastUpdatedProjectStatus
                        if (newStatus > lastUpdatedProjectStatus) {
                            Log.d("TAG", "Updating project status from $lastUpdatedProjectStatus to $newStatus")

                            baasViewModel.updateProject(
                                changeCount = 0,
                                customerId = null,
                                userId = null,
                                status = newStatus,
                                profitOverhead = null,
                                hourlyRate = null,
                                id = projectId
                            ).observe(viewLifecycleOwner) { updateResult ->
                                when (updateResult.status) {
                                    Status.LOADING -> {
                                        Log.d("TAG", "Updating project status...")
                                    }
                                    Status.SUCCESS -> {
                                        if (updateResult.data?.error == true) {
                                            Log.d("TAG", "Error updating project status: ${updateResult.data.message}")
                                        } else {
                                            Log.d("TAG", "Project status successfully updated to $newStatus")
                                            lastUpdatedProjectStatus = newStatus
                                        }
                                    }
                                    Status.ERROR -> {
                                        Log.d("TAG", "Error updating project status: ${updateResult.message}")
                                    }
                                }
                            }
                        } else {
                            Log.d("TAG", "No update needed, current status $lastUpdatedProjectStatus is >= new status $newStatus")
                        }
                    }
                }
            }
        }
    }

}