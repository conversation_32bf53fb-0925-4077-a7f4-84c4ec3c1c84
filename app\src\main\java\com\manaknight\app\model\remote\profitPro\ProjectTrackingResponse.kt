// In com.manaknight.app.model.remote.profitPro.ProjectTrackingResponse.kt
package com.manaknight.app.model.remote.profitPro

import com.google.gson.annotations.SerializedName

data class ProjectTrackingResponse(
    @SerializedName("data")
    val trackingData: TrackingData?,
    val error: Boolean?,
)

data class TrackingData(
    val draws: Draws?,
    val labor: Labor?,
    val materials: Materials?
)

data class Draws(
    val list: List<DrawItem>?,
    @SerializedName("unaccounted_amount")
    val unaccountedAmount: String?,
    @SerializedName("sale_price")
    val salePrice: String?
)

data class DrawItem(
    @SerializedName("amount_paid")
    val amountPaid: String?,
    val id: Int?,
    @SerializedName("check_no")
    val checkNo: String?,
    val amount: String?,
    val percentage: String?,
    val description: String?,
    @SerializedName("create_at")
    val createdAt: String?,
    @SerializedName("update_at")
    val updatedAt: String?,
    val status: String?,
    @SerializedName("project_id")
    val projectId: Int?,
    @SerializedName("payment_type")
    val paymentType: String?,
    @SerializedName("status_type")
    val statusType: String?,
    @SerializedName("status_label")
    val statusLabel: String?,
    val remainingBalance: String?,
    val remainingPercentage: String?
)

data class Labor(
    @SerializedName("labor_budget")
    val laborBudget: Double?,
    @SerializedName("labor_spent")
    val laborSpent: Double?,
    @SerializedName("labor_balance")
    val laborBalance: Double?,
    @SerializedName("labor_spent_percentage")
    val laborSpentPercentage: String?,
    @SerializedName("remaining_percentage")
    val remainingPercentage: String?,
    @SerializedName("team_members")
    val teamMembers: List<TeamMembers>? // You might want a specific model for team members if needed
)

data class Materials(
    @SerializedName("material_budget")
    val materialBudget: Double?,
    @SerializedName("material_spent")
    val materialSpent: Double?,
    @SerializedName("material_balance")
    val materialBalance: Double?,
    val materials: List<MaterialItem>?
)

data class MaterialItem(
    val id: Int?,
    @SerializedName("create_at")
    val createdAt: String?,
    @SerializedName("update_at")
    val updatedAt: String?,
    @SerializedName("user_id")
    val userId: String?,
    val cost: String?,
    val name: String?,
    val hidden: Int?
)


data class TeamMembers(
    val total: Int?,
    val id: Int?,
    val hours: String?,
    @SerializedName("hourly_rate")
    val hourlyRate: String?,
    val cost: String?,
    val name: String?,
)