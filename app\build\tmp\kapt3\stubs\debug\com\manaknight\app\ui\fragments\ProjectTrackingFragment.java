package com.manaknight.app.ui.fragments;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000T\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u000eH\u0002J \u0010\u0012\u001a\u00020\u00102\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u00142\u0006\u0010\u0016\u001a\u00020\u000eH\u0002J\u0018\u0010\u0017\u001a\u00020\u00102\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u0014H\u0002J \u0010\u0018\u001a\u00020\u00102\u0006\u0010\u0019\u001a\u00020\u000e2\u0006\u0010\u001a\u001a\u00020\u00142\u0006\u0010\u001b\u001a\u00020\u001cH\u0002J$\u0010\u001d\u001a\u00020\u001e2\u0006\u0010\u001f\u001a\u00020 2\b\u0010!\u001a\u0004\u0018\u00010\"2\b\u0010#\u001a\u0004\u0018\u00010$H\u0016R\u001b\u0010\u0003\u001a\u00020\u00048BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0007\u0010\b\u001a\u0004\b\u0005\u0010\u0006R\u000e\u0010\t\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006%"}, d2 = {"Lcom/manaknight/app/ui/fragments/ProjectTrackingFragment;", "Landroidx/fragment/app/Fragment;", "()V", "baasViewModel", "Lcom/manaknight/app/viewmodels/BaasViewModel;", "getBaasViewModel", "()Lcom/manaknight/app/viewmodels/BaasViewModel;", "baasViewModel$delegate", "Lkotlin/Lazy;", "dialog", "Landroid/app/Dialog;", "hasUpdatedProjectStatus", "", "lastUpdatedProjectStatus", "", "checkAndUpdateProjectStatus", "", "projectId", "handleMaterialEdit", "materialName", "", "unitCost", "id", "handleMaterialSave", "handleUpdateDraws", "paymentMethodId", "checkNumber", "drawItem", "Lcom/manaknight/app/model/remote/profitPro/DrawItem;", "onCreateView", "Landroid/view/View;", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "savedInstanceState", "Landroid/os/Bundle;", "app_debug"})
public final class ProjectTrackingFragment extends androidx.fragment.app.Fragment {
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy baasViewModel$delegate = null;
    private android.app.Dialog dialog;
    private boolean hasUpdatedProjectStatus = false;
    private int lastUpdatedProjectStatus = -1;
    
    public ProjectTrackingFragment() {
        super();
    }
    
    private final com.manaknight.app.viewmodels.BaasViewModel getBaasViewModel() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull()
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable()
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    private final void handleMaterialSave(java.lang.String materialName, java.lang.String unitCost) {
    }
    
    private final void handleMaterialEdit(java.lang.String materialName, java.lang.String unitCost, int id) {
    }
    
    private final void handleUpdateDraws(int paymentMethodId, java.lang.String checkNumber, com.manaknight.app.model.remote.profitPro.DrawItem drawItem) {
    }
    
    private final void checkAndUpdateProjectStatus(int projectId) {
    }
}