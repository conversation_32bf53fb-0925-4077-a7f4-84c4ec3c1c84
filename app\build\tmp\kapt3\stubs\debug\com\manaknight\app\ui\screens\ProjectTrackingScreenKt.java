package com.manaknight.app.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000v\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0010\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0012\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\b\u001a0\u0010\u0000\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\u0018\u0010\u0004\u001a\u0014\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001a@\u0010\u0007\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\u00062\u0012\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\n2\u0006\u0010\u000b\u001a\u00020\u00062\u0012\u0010\f\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\nH\u0007\u001a@\u0010\r\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\u00062\u0006\u0010\u000b\u001a\u00020\u00062\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\u0018\u0010\u0004\u001a\u0014\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001a8\u0010\u000e\u001a\u00020\u00012\u0006\u0010\u000f\u001a\u00020\u00102\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\u0018\u0010\u0004\u001a\u0014\u0012\u0004\u0012\u00020\u0011\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001a*\u0010\u0012\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\u0012\u0010\u0013\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\nH\u0007\u001aX\u0010\u0014\u001a\u00020\u00012\b\u0010\u0015\u001a\u0004\u0018\u00010\u00162\b\b\u0002\u0010\u0017\u001a\u00020\u00062\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0019\u0012\u0004\u0012\u00020\u00010\n2\u0012\u0010\u001a\u001a\u000e\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u00020\u00010\n2\u0012\u0010\u001b\u001a\u000e\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u00020\u00010\nH\u0007\u001a\u0010\u0010\u001c\u001a\u00020\u00012\u0006\u0010\u001d\u001a\u00020\u0006H\u0007\u001a\u001e\u0010\u001e\u001a\u00020\u00012\u0006\u0010\u0017\u001a\u00020\u00062\f\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001a\u0016\u0010 \u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001aH\u0010!\u001a\u00020\u00012\u0006\u0010\"\u001a\u00020\u00062\u0012\u0010#\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\n2\u0006\u0010$\u001a\u00020\u00062\u0012\u0010%\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\n2\u0006\u0010&\u001a\u00020\u0006H\u0007\u001a:\u0010\'\u001a\u00020\u00012\u0006\u0010\"\u001a\u00020\u00062\u0006\u0010$\u001a\u00020\u00062\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\nH\u0007\u001a>\u0010(\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\u0006\u0010)\u001a\u00020*2\u001e\u0010\u0004\u001a\u001a\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0011\u0012\u0004\u0012\u00020\u00010+H\u0007\u001a@\u0010,\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\u00062\u0012\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\n2\u0006\u0010\u000b\u001a\u00020\u00062\u0012\u0010\f\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\nH\u0007\u001aN\u0010-\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\u00062\u0006\u0010\u000b\u001a\u00020\u00062\u0006\u0010.\u001a\u00020\u00112\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\u001e\u0010\u0004\u001a\u001a\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0011\u0012\u0004\u0012\u00020\u00010+H\u0007\u001a:\u0010/\u001a\u00020\u00012\u0006\u0010\u000f\u001a\u00020\u00102\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u00100\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u00101\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001a@\u00102\u001a\u00020\u00012\u0006\u00103\u001a\u0002042\f\u00105\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032 \b\u0002\u00106\u001a\u001a\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u000207\u0012\u0004\u0012\u00020\u00010+H\u0007\u001a$\u00108\u001a\u00020\u00012\u0006\u00109\u001a\u0002072\u0012\u0010:\u001a\u000e\u0012\u0004\u0012\u000207\u0012\u0004\u0012\u00020\u00010\nH\u0007\u001a \u0010;\u001a\u00020\u00012\u0006\u0010\u001d\u001a\u00020\u00062\u0006\u0010<\u001a\u00020\u00062\u0006\u0010=\u001a\u00020\u0006H\u0007\u001af\u0010>\u001a\u00020\u00012\u0006\u0010?\u001a\u00020\u00112\u0006\u0010@\u001a\u00020\u00062\b\u0010A\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010B\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010C\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010D\u001a\u0004\u0018\u00010\u00062\u000e\b\u0002\u0010E\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\u000e\b\u0002\u0010F\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001a$\u0010G\u001a\u00020\u00012\u0006\u0010)\u001a\u00020*2\u0012\u0010:\u001a\u000e\u0012\u0004\u0012\u00020*\u0012\u0004\u0012\u00020\u00010\nH\u0007\u001a>\u0010H\u001a\u00020\u00012\u0006\u0010I\u001a\u00020J2\f\u0010K\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\u001e\u0010L\u001a\u001a\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0011\u0012\u0004\u0012\u00020\u00010+H\u0007\u001a\u00a4\u0001\u0010M\u001a\u00020\u00012\u0006\u0010N\u001a\u00020\u00112\u0006\u0010O\u001a\u00020\u00062\u0006\u0010P\u001a\u00020Q2\u0006\u0010R\u001a\u00020S2\u0006\u0010T\u001a\u00020U2\u0018\u0010V\u001a\u0014\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u00052\u001e\u0010L\u001a\u001a\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0011\u0012\u0004\u0012\u00020\u00010+2\u001e\u0010W\u001a\u001a\u0012\u0004\u0012\u00020\u0011\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u00020\u00010+2\u0018\u00106\u001a\u0014\u0012\u0004\u0012\u00020\u0011\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001a&\u0010X\u001a\u00020\u00012\u0006\u0010Y\u001a\u00020\u00062\u0006\u0010Z\u001a\u00020\u00192\f\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001a\u001a\u0010[\u001a\u00020\u00012\u0006\u0010C\u001a\u00020\u00062\b\u0010D\u001a\u0004\u0018\u00010\u0006H\u0007\u001a\u0012\u0010\\\u001a\u0004\u0018\u00010\u00062\b\u0010C\u001a\u0004\u0018\u00010\u0006\u00a8\u0006]"}, d2 = {"AddMaterialExpenseSheet", "", "onDismiss", "Lkotlin/Function0;", "onSave", "Lkotlin/Function2;", "", "AddMaterialExpenseSheetContentBody", "materialName", "onMaterialNameChange", "Lkotlin/Function1;", "unitCost", "onUnitCostChange", "AddMaterialExpenseSheetHeader", "AddPaymentMethodSheet", "drawItem", "Lcom/manaknight/app/model/remote/profitPro/DrawItem;", "", "DrawStatusFilterSheet", "onStatusSelected", "DrawsContent", "drawData", "Lcom/manaknight/app/model/remote/profitPro/Draws;", "selectedStatus", "showStatusFilter", "", "onShowPaymentMethodSheet", "onShowInvoiceOptionsSheet", "DrawsDropdownFilter", "label", "DrawsStatusFilterDropdown", "onClick", "EditLaborSheet", "EditLaborSheetContentBody", "employeeName", "onEmployeeNameChange", "hours", "onHoursChange", "hourlyRate", "EditLaborSheetHeader", "EditMaterialExpenseSheet", "materialItem", "Lcom/manaknight/app/model/remote/profitPro/MaterialItem;", "Lkotlin/Function3;", "EditMaterialExpenseSheetContentBody", "EditMaterialExpenseSheetHeader", "materialId", "InvoiceOptionsSheet", "onViewInvoice", "onSendInvoice", "LaborContent", "laborData", "Lcom/manaknight/app/model/remote/profitPro/Labor;", "onLaborEditClick", "onEditLabor", "Lcom/manaknight/app/model/remote/profitPro/TeamMembers;", "LaborCostItem", "teamMember", "onEditClick", "LaborSummaryItem", "value", "percentageValue", "ListItem", "number", "amount", "badge", "description", "status", "date", "onMoreOptionsClick", "onCheckmarkClick", "MaterialItem", "MaterialsContent", "materialsData", "Lcom/manaknight/app/model/remote/profitPro/Materials;", "onNewMaterialClick", "onEditMaterial", "ProjectTrackingScreen", "projectId", "projectName", "baasViewModel", "Lcom/manaknight/app/viewmodels/BaasViewModel;", "dialog", "Landroid/app/Dialog;", "navController", "Landroidx/navigation/NavController;", "onSaveMaterial", "onUpdateDraws", "TabButton", "text", "selected", "TrackingStatusBadge", "getDrawStatusText", "app_debug"})
public final class ProjectTrackingScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void ProjectTrackingScreen(int projectId, @org.jetbrains.annotations.NotNull()
    java.lang.String projectName, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.viewmodels.BaasViewModel baasViewModel, @org.jetbrains.annotations.NotNull()
    android.app.Dialog dialog, @org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.String, kotlin.Unit> onSaveMaterial, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super java.lang.String, ? super java.lang.String, ? super java.lang.Integer, kotlin.Unit> onEditMaterial, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super java.lang.Integer, ? super java.lang.String, ? super com.manaknight.app.model.remote.profitPro.DrawItem, kotlin.Unit> onUpdateDraws, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.Integer, ? super java.lang.String, kotlin.Unit> onEditLabor) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void TabButton(@org.jetbrains.annotations.NotNull()
    java.lang.String text, boolean selected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void DrawsContent(@org.jetbrains.annotations.Nullable()
    com.manaknight.app.model.remote.profitPro.Draws drawData, @org.jetbrains.annotations.NotNull()
    java.lang.String selectedStatus, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> showStatusFilter, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.manaknight.app.model.remote.profitPro.DrawItem, kotlin.Unit> onShowPaymentMethodSheet, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.manaknight.app.model.remote.profitPro.DrawItem, kotlin.Unit> onShowInvoiceOptionsSheet) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void DrawStatusFilterSheet(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onStatusSelected) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void DrawsStatusFilterDropdown(@org.jetbrains.annotations.NotNull()
    java.lang.String selectedStatus, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void DrawsDropdownFilter(@org.jetbrains.annotations.NotNull()
    java.lang.String label) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void LaborContent(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.Labor laborData, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onLaborEditClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super java.lang.String, ? super java.lang.String, ? super com.manaknight.app.model.remote.profitPro.TeamMembers, kotlin.Unit> onEditLabor) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void LaborSummaryItem(@org.jetbrains.annotations.NotNull()
    java.lang.String label, @org.jetbrains.annotations.NotNull()
    java.lang.String value, @org.jetbrains.annotations.NotNull()
    java.lang.String percentageValue) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void LaborCostItem(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.TeamMembers teamMember, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.manaknight.app.model.remote.profitPro.TeamMembers, kotlin.Unit> onEditClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ListItem(int number, @org.jetbrains.annotations.NotNull()
    java.lang.String amount, @org.jetbrains.annotations.Nullable()
    java.lang.String badge, @org.jetbrains.annotations.Nullable()
    java.lang.String description, @org.jetbrains.annotations.Nullable()
    java.lang.String status, @org.jetbrains.annotations.Nullable()
    java.lang.String date, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onMoreOptionsClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onCheckmarkClick) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public static final java.lang.String getDrawStatusText(@org.jetbrains.annotations.Nullable()
    java.lang.String status) {
        return null;
    }
    
    @androidx.compose.runtime.Composable()
    public static final void TrackingStatusBadge(@org.jetbrains.annotations.NotNull()
    java.lang.String status, @org.jetbrains.annotations.Nullable()
    java.lang.String date) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void MaterialsContent(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.Materials materialsData, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNewMaterialClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super java.lang.String, ? super java.lang.String, ? super java.lang.Integer, kotlin.Unit> onEditMaterial) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void MaterialItem(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.MaterialItem materialItem, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.manaknight.app.model.remote.profitPro.MaterialItem, kotlin.Unit> onEditClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AddMaterialExpenseSheet(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.String, kotlin.Unit> onSave) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void EditMaterialExpenseSheet(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.MaterialItem materialItem, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super java.lang.String, ? super java.lang.String, ? super java.lang.Integer, kotlin.Unit> onSave) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void EditLaborSheet(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void InvoiceOptionsSheet(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.DrawItem drawItem, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onViewInvoice, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onSendInvoice) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AddPaymentMethodSheet(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.DrawItem drawItem, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.Integer, ? super java.lang.String, kotlin.Unit> onSave) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AddMaterialExpenseSheetHeader(@org.jetbrains.annotations.NotNull()
    java.lang.String materialName, @org.jetbrains.annotations.NotNull()
    java.lang.String unitCost, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.String, kotlin.Unit> onSave) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AddMaterialExpenseSheetContentBody(@org.jetbrains.annotations.NotNull()
    java.lang.String materialName, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onMaterialNameChange, @org.jetbrains.annotations.NotNull()
    java.lang.String unitCost, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onUnitCostChange) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void EditMaterialExpenseSheetHeader(@org.jetbrains.annotations.NotNull()
    java.lang.String materialName, @org.jetbrains.annotations.NotNull()
    java.lang.String unitCost, int materialId, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super java.lang.String, ? super java.lang.String, ? super java.lang.Integer, kotlin.Unit> onSave) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void EditMaterialExpenseSheetContentBody(@org.jetbrains.annotations.NotNull()
    java.lang.String materialName, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onMaterialNameChange, @org.jetbrains.annotations.NotNull()
    java.lang.String unitCost, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onUnitCostChange) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void EditLaborSheetHeader(@org.jetbrains.annotations.NotNull()
    java.lang.String employeeName, @org.jetbrains.annotations.NotNull()
    java.lang.String hours, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onSave) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void EditLaborSheetContentBody(@org.jetbrains.annotations.NotNull()
    java.lang.String employeeName, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onEmployeeNameChange, @org.jetbrains.annotations.NotNull()
    java.lang.String hours, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onHoursChange, @org.jetbrains.annotations.NotNull()
    java.lang.String hourlyRate) {
    }
}